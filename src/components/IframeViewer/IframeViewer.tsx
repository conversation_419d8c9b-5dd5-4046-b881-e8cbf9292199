import React, {
  useState,
  forwardRef,
} from 'react';
import { Box, CircularProgress } from '@mui/material';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

const DEFAULT_SANDBOX = 'allow-same-origin allow-scripts allow-forms allow-popups';
const FALLBACK_LOADER_COLOR = '#1976d2';

const getLoaderColor = (themeName: string): string => {
  try {
    const baseColor = styles?.brandBlueColor500 || '#2D2A81';
    const themeColors = getBrandColors(baseColor, themeName);

    if (themeColors && typeof themeColors === 'object' && themeColors[500]) {
      return themeColors[500];
    }

    return baseColor;
  } catch (error) {
    console.warn('Failed to get theme colors for IframeViewer loader, using fallback color:', error);
    return FALLBACK_LOADER_COLOR;
  }
};

export interface IframeViewerProps extends React.IframeHTMLAttributes<HTMLIFrameElement> {
  src: string;
  title?: string;
  className?: string;
  style?: React.CSSProperties;
  showLoading?: boolean;
  onLoad?: () => void;
  sandbox?: string;
  themeName?: string;
}

const IframeViewer = forwardRef<HTMLIFrameElement, IframeViewerProps>(({
  src,
  title = '',
  className,
  style = {},
  showLoading = true,
  onLoad,
  sandbox = DEFAULT_SANDBOX,
  themeName = 'bt',
  ...rest
}, ref) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const loaderColor = getLoaderColor(themeName);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  return (
    <Box className='iframe-viewer-wrap'>
      {isLoading && showLoading && (
        <Box
          data-testid="loader"
          className="iframe-loader"
        >
          <CircularProgress
            sx={{
              color: loaderColor,
            }}
          />
        </Box>
      )}

      <iframe
        className={`iframe-style ${className || ''}`}
        ref={ref}
        src={src}
        title={title}
        style={{ ...style }}
        sandbox={sandbox}
        allowFullScreen
        onLoad={handleLoad}
        {...rest}
      />
    </Box>
  );
});

IframeViewer.displayName = 'IframeViewer';

export default IframeViewer;
