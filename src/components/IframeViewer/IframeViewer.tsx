import React, {
  useState,
  forwardRef,
} from 'react';
import { Box, CircularProgress } from '@mui/material';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

const DEFAULT_SANDBOX = 'allow-same-origin allow-scripts allow-forms allow-popups';

export interface IframeViewerProps extends React.IframeHTMLAttributes<HTMLIFrameElement> {
  src: string;
  title?: string;
  className?: string;
  style?: React.CSSProperties;
  showLoading?: boolean;
  onLoad?: () => void;
  sandbox?: string;
  themeName?: string;
}

const IframeViewer = forwardRef<HTMLIFrameElement, IframeViewerProps>(({
  src,
  title = '',
  className,
  style = {},
  showLoading = true,
  onLoad,
  sandbox = DEFAULT_SANDBOX,
  themeName = 'bt',
  ...rest
}, ref) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const themeColors = getBrandColors(styles.brandBlueColor500, themeName);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  return (
    <Box className='iframe-viewer-wrap'>
      {isLoading && showLoading && (
        <Box
          data-testid="loader"
          className="iframe-loader"
        >
          <CircularProgress
            sx={{
              color: themeColors[500],
            }}
          />
        </Box>
      )}

      <iframe
        className={`iframe-style ${className || ''}`}
        ref={ref}
        src={src}
        title={title}
        style={{ ...style }}
        sandbox={sandbox}
        allowFullScreen
        onLoad={handleLoad}
        {...rest}
      />
    </Box>
  );
});

IframeViewer.displayName = 'IframeViewer';

export default IframeViewer;
