import React from 'react';
import { IframeViewer } from 'components/IframeViewer';

export default {
  title: 'Components/IframeViewer',
  component: IframeViewer,
  argTypes: {
    src: {
      control: 'text',
      description: 'The URL to load in the iframe',
    },
    title: {
      control: 'text',
      description: 'The title attribute for the iframe',
    },
    showLoading: {
      control: 'boolean',
      description: 'Whether to show loading indicator while iframe loads',
    },
    sandbox: {
      control: 'text',
      description: 'Sandbox restrictions for the iframe',
    },
    style: {
      control: 'object',
      description: 'Custom styles for the iframe',
    },
    className: {
      control: 'text',
      description: 'CSS class name for the container',
    },
  },
};

const Template = (args) => (
  <div style={{ height: '400px', border: '1px solid #ccc' }}>
    <IframeViewer {...args} />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  src: 'https://www.example.com',
  title: 'Example Website',
  showLoading: true,
  themeName: 'bt',
  style: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
};

export const WithCustomSandbox = Template.bind({});
WithCustomSandbox.args = {
  src: 'https://www.example.com',
  title: 'Restricted Example',
  showLoading: true,
  themeName: 'bt',
  sandbox: 'allow-scripts allow-same-origin',
  style: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
};

export const NoLoadingIndicator = Template.bind({});
NoLoadingIndicator.args = {
  src: 'https://www.example.com',
  title: 'No Loading Example',
  showLoading: false,
  themeName: 'bt',
  style: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
};

export const CustomStyling = Template.bind({});
CustomStyling.args = {
  src: 'https://www.example.com',
  title: 'Custom Styled Example',
  showLoading: true,
  themeName: 'bt',
  className: 'custom-iframe-container',
  style: {
    width: '100%',
    height: '100%',
    border: '2px solid #007bff',
    borderRadius: '8px',
  },
};

export const EmbeddedMap = Template.bind({});
EmbeddedMap.args = {
  src: 'https://www.openstreetmap.org/export/embed.html?bbox=-0.004017949104309083%2C51.47612752641776%2C0.00030577182769775396%2C51.478569861898606&layer=mapnik',
  title: 'OpenStreetMap Example',
  showLoading: true,
  themeName: 'bt',
  style: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
};
